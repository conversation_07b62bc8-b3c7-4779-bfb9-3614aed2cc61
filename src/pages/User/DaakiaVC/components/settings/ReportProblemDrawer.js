import React, { useState } from "react";
import <PERSON><PERSON> from "lottie-react";
import { Checkbox, Button } from "antd";
import TextArea from "antd/lib/input/TextArea";
import SideDrawer from "../SideDrawer";
import sendMessageAnimation from "./icons/success.json";

import { SettingsMenuServices } from "../../services/SettingsMenuServices";

import "../../styles/ReportProblem.scss";
import "../../styles/index.scss";
import { useVideoConferencesContext } from "../../context/VideoConferencesContext";
import { useSaasHelpers } from "../../SaaS/helpers/helpers";

export function ReportProblemDrawer({
  isRPDrawerOpen,
  // setIsRPDrawerOpen,
  id,
  clientPreferedServerId,
  isWhiteboardOpen,
  localParticipant,
  setToastNotification,
  setToastStatus,
  setShowToast,
  meetingFeatures,
}) {
  const { setOpenDrawer } = useVideoConferencesContext();
  const [checkedItems, setCheckedItems] = useState({
    poorAudioQuality: false,
    poorVideoQuality: false,
    screenShareQuality: false,
  });
  const [problemReported, setProblemReported] = useState(false);
  const [textAreaValue, setTextAreaValue] = useState("");
  const { saasHostToken } = useSaasHelpers();

  const onChange = (e) => {
    const { value, checked } = e.target;
    setCheckedItems((prev) => ({
      ...prev,
      [value]: checked,
    }));
  };

  const handleTextChange = (e) => {
    setTextAreaValue(e.target.value);
  };

  const handleSubmit = async () => {
    if (textAreaValue.length === 0) {
      setToastNotification("Please enter your concern.");
      setToastStatus("error");
      setShowToast(true);
      return;
    }
    try {
      const response = await SettingsMenuServices.reportAProblem(
        id,
        clientPreferedServerId,
        textAreaValue,
        checkedItems.poorAudioQuality,
        checkedItems.poorVideoQuality,
        checkedItems.screenShareQuality,
        localParticipant?.participantInfo,
        saasHostToken
      );
      if (response.success === 0) {
        setToastNotification(response.message);
        setToastStatus("error");
        setShowToast(true);
        throw new Error(response.message);
      } else {
        // setToastNotification("Your concern has been sent to Team Daakia and will be resolved soon!");
        // setToastStatus("success");
        // setShowToast(true);
        setProblemReported(true);
      }
    } catch (error) {
      setToastNotification(error.message);
      setToastStatus("error");
      setShowToast(true);
      // console.error("Error reporting problem:", error);
    } finally {
      setTimeout(() => {
        // setIsRPDrawerOpen(false);
        setOpenDrawer(null);
        setProblemReported(false);
      }, 5000);
    }
  };

  return (
    <SideDrawer
      show={isRPDrawerOpen}
      // setShow={setIsRPDrawerOpen}
      title="Report a Problem"
      style={{ gap: "1rem" }}
      className={`rtp`}
      isWhiteboardOpen={isWhiteboardOpen}
    >
      {problemReported ? (
        <div className="rtp-success">
          <span className="rtp-success-icon">
            <Lottie
              animationData={sendMessageAnimation}
              loop={false} // Set to true for continuous loop, false to play once
              style={{ width: 150, height: 150 }} // Adjust size as needed
            />
          </span>
          <span className="rtp-success-message">
            {meetingFeatures?.configurations
              ? meetingFeatures?.configurations?.branding_enabled && meetingFeatures?.configurations?.branding_app_title
                ? `Your concern has been sent to Team ${meetingFeatures?.configurations?.branding_app_title} and will be resolved soon!`
                : "Your concern has been sent to Team Daakia and will be resolved soon!"
              : "Your concern has been sent to Team Daakia and will be resolved soon!"}
          </span>
        </div>
      ) : (
        <>
          <p className="rtp-heading">Check all that apply</p>
          <div className="rtp-checkboxes">
            <Checkbox value="poorAudioQuality" onChange={onChange}>
              <p>Poor audio quality</p>
            </Checkbox>
            <Checkbox value="poorVideoQuality" onChange={onChange}>
              <p>Poor video quality</p>
            </Checkbox>
            <Checkbox value="screenShareQuality" onChange={onChange}>
              <p>Screen share quality</p>
            </Checkbox>
          </div>
          <hr className="rtp-hr" />
          <div className="rtp-other-issues">
            <p>
              If your issue isn&apos;t listed, describe it and we&apos;ll assist
              you promptly.
            </p>
            <div className="rtp-input-box">
              <TextArea
                maxLength={150}
                showCount
                value={textAreaValue}
                onChange={handleTextChange}
              />
            </div>
            <div className="rtp-buttons">
              <Button
                type="primary"
                size="large"
                className="ls-button ls-button-cancel"
                ghost
                onClick={() => {
                  // setIsRPDrawerOpen(false);
                  setOpenDrawer(null);
                }}
              >
                CANCEL
              </Button>
              <Button
                type="primary"
                size="large"
                className="ls-button"
                onClick={handleSubmit}
              >
                SUBMIT
              </Button>
            </div>
          </div>
        </>
      )}
    </SideDrawer>
  );
}
